#!/usr/bin/env node

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Comprehensive Test Runner for M-Pesa Integration
 * 
 * This script runs different types of tests based on the provided arguments:
 * - unit: Run unit tests only
 * - integration: Run integration tests only
 * - e2e: Run end-to-end tests only
 * - sandbox: Run M-Pesa sandbox tests (requires valid credentials)
 * - all: Run all tests except sandbox
 * - coverage: Run all tests with coverage report
 */

class TestRunner {
  constructor() {
    this.testTypes = {
      unit: 'tests/unit/**/*.test.js',
      integration: 'tests/integration/**/*.test.js',
      e2e: 'tests/e2e/**/*.test.js',
      sandbox: 'tests/sandbox/**/*.js'
    };
  }

  async run(testType = 'all') {
    console.log('🧪 M-Pesa Integration Test Runner');
    console.log('==================================\n');

    // Ensure test environment is set up
    await this.setupTestEnvironment();

    switch (testType.toLowerCase()) {
      case 'unit':
        await this.runUnitTests();
        break;
      case 'integration':
        await this.runIntegrationTests();
        break;
      case 'e2e':
        await this.runE2ETests();
        break;
      case 'sandbox':
        await this.runSandboxTests();
        break;
      case 'coverage':
        await this.runWithCoverage();
        break;
      case 'all':
      default:
        await this.runAllTests();
        break;
    }
  }

  async setupTestEnvironment() {
    console.log('🔧 Setting up test environment...');

    // Ensure test directories exist
    const testDirs = ['tests/logs', 'coverage'];
    for (const dir of testDirs) {
      const fullPath = path.join(__dirname, '..', dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
        console.log(`   Created directory: ${dir}`);
      }
    }

    // Check if .env.test exists
    const envTestPath = path.join(__dirname, '..', '.env.test');
    if (!fs.existsSync(envTestPath)) {
      console.log('⚠️  Warning: .env.test file not found. Some tests may fail.');
      console.log('   Please copy .env.example to .env.test and configure test values.');
    }

    console.log('✅ Test environment ready\n');
  }

  async runUnitTests() {
    console.log('🔬 Running Unit Tests...');
    await this.runJest(this.testTypes.unit, 'Unit Tests');
  }

  async runIntegrationTests() {
    console.log('🔗 Running Integration Tests...');
    await this.runJest(this.testTypes.integration, 'Integration Tests');
  }

  async runE2ETests() {
    console.log('🎭 Running End-to-End Tests...');
    await this.runJest(this.testTypes.e2e, 'End-to-End Tests');
  }

  async runSandboxTests() {
    console.log('🏖️  Running M-Pesa Sandbox Tests...');
    console.log('⚠️  Note: This requires valid M-Pesa sandbox credentials\n');

    try {
      await this.runCommand('node', ['tests/sandbox/mpesaSandboxTest.js'], 'M-Pesa Sandbox Tests');
    } catch (error) {
      console.log('❌ Sandbox tests failed. This is expected if M-Pesa credentials are not configured.');
      console.log('   Configure your .env.test file with valid M-Pesa sandbox credentials to run these tests.\n');
    }
  }

  async runAllTests() {
    console.log('🚀 Running All Tests (except sandbox)...\n');

    try {
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runE2ETests();
      
      console.log('🎉 All test suites completed successfully!');
      console.log('\n💡 To run M-Pesa sandbox tests, use: npm run test:sandbox');
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async runWithCoverage() {
    console.log('📊 Running All Tests with Coverage Report...\n');

    const jestArgs = [
      '--coverage',
      '--coverageDirectory=coverage',
      '--coverageReporters=text,lcov,html',
      '--testPathPattern=(unit|integration|e2e)',
      '--verbose'
    ];

    await this.runJest('tests/**/*.test.js', 'All Tests with Coverage', jestArgs);
    
    console.log('\n📈 Coverage report generated in ./coverage directory');
    console.log('   Open ./coverage/lcov-report/index.html in your browser to view detailed coverage');
  }

  async runJest(testPattern, testName, additionalArgs = []) {
    const args = [
      '--testPathPattern=' + testPattern,
      '--verbose',
      '--detectOpenHandles',
      '--forceExit',
      ...additionalArgs
    ];

    await this.runCommand('npx', ['jest', ...args], testName);
  }

  async runCommand(command, args, testName) {
    return new Promise((resolve, reject) => {
      console.log(`Running: ${command} ${args.join(' ')}\n`);

      const process = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        cwd: path.join(__dirname, '..')
      });

      process.on('close', (code) => {
        if (code === 0) {
          console.log(`\n✅ ${testName} completed successfully\n`);
          resolve();
        } else {
          console.log(`\n❌ ${testName} failed with exit code ${code}\n`);
          reject(new Error(`${testName} failed`));
        }
      });

      process.on('error', (error) => {
        console.error(`\n❌ Failed to start ${testName}:`, error.message);
        reject(error);
      });
    });
  }

  printUsage() {
    console.log('Usage: npm run test [type]');
    console.log('\nTest types:');
    console.log('  unit        - Run unit tests only');
    console.log('  integration - Run integration tests only');
    console.log('  e2e         - Run end-to-end tests only');
    console.log('  sandbox     - Run M-Pesa sandbox tests (requires credentials)');
    console.log('  coverage    - Run all tests with coverage report');
    console.log('  all         - Run all tests except sandbox (default)');
    console.log('\nExamples:');
    console.log('  npm run test unit');
    console.log('  npm run test:coverage');
    console.log('  npm run test:sandbox');
  }
}

// Parse command line arguments
const testType = process.argv[2] || 'all';

if (testType === 'help' || testType === '--help' || testType === '-h') {
  const runner = new TestRunner();
  runner.printUsage();
  process.exit(0);
}

// Run tests
const runner = new TestRunner();
runner.run(testType).catch(error => {
  console.error('Test runner failed:', error.message);
  process.exit(1);
});
